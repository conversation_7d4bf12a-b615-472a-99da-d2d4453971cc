BlazePose TensorFlow.js Implementation: Complete Autopsy Report
Executive Summary
BlazePose is not a standalone module - it's a complex orchestration of 42+ shared utility functions from the parent pose-detection library. Your current implementation is missing the critical tensor processing pipeline that transforms raw model outputs into usable 3D pose data.
🏗️ Architecture Overview
BlazePose Pipeline
├── Input Processing
│   ├── Image Tensor Conversion
│   └── Letterboxing & Padding
├── Detection Phase (Pose Detection)
│   ├── SSD Anchor Generation
│   ├── Tensor to Detection Conversion
│   └── Non-Max Suppression
├── Landmark Phase (Pose Landmarks)
│   ├── ROI Extraction
│   ├── Landmark Model Inference
│   ├── Tensor to Landmark Conversion
│   ├── Heatmap Refinement
│   └── 3D World Coordinate Projection
├── Post-Processing
│   ├── Coordinate Denormalization
│   ├── Temporal Smoothing
│   └── Segmentation Mask Processing
└── Output
    ├── 2D Keypoints (39 points)
    ├── 3D World Coordinates
    └── Segmentation Mask
📁 Complete File Dependency Map
Core BlazePose Files (blazepose_tfjs/)
	1	detector.ts - Main orchestrator
	2	constants.ts - Configuration parameters
	3	detector_utils.ts - Validation utilities
	4	types.ts - TypeScript interfaces
Critical Shared Dependencies (../shared/)
🔴 MISSING IN YOUR IMPLEMENTATION - Calculators Directory
shared/calculators/
├── calculate_alignment_points_rects.ts
├── calculate_inverse_matrix.ts
├── calculate_landmark_projection.ts
├── calculate_score_copy.ts
├── calculate_world_landmark_projection.ts
├── convert_image_to_tensor.ts
├── create_ssd_anchors.ts
├── detector_result.ts
├── image_utils.ts
├── is_video.ts
├── landmarks_to_detection.ts
├── mask_util.ts
├── non_max_suppression.ts
├── normalized_keypoints_to_keypoints.ts
├── refine_landmarks_from_heatmap.ts
├── remove_detection_letterbox.ts
├── remove_landmark_letterbox.ts
├── segmentation_smoothing.ts
├── tensors_to_detections.ts
├── tensors_to_landmarks.ts
├── tensors_to_segmentation.ts
└── transform_rect.ts
🔴 MISSING IN YOUR IMPLEMENTATION - Filters Directory
shared/filters/
├── keypoints_smoothing.ts
└── visibility_smoothing.ts
🔬 Detailed Function Analysis
Phase 1: Detection Pipeline
1.1 detectPose() - Entry Point
// Location: detector.ts, line ~265
private async detectPose(image: PoseDetectorInput): Promise<Detection[]>
Purpose: Detects potential pose regions in the image Dependencies:
	•	convertImageToTensor() - Preprocesses image with letterboxing
	•	createSsdAnchors() - Generates anchor boxes for detection
	•	detectorResult() - Extracts boxes and logits from model output
	•	tensorsToDetections() - Converts raw tensors to detection objects
	•	nonMaxSuppression() - Filters overlapping detections
	•	removeDetectionLetterbox() - Adjusts coordinates for original image
1.2 poseDetectionToRoi() - ROI Calculation
// Location: detector.ts, line ~314
private poseDetectionToRoi(detection: Detection, imageSize?: ImageSize): Rect
Purpose: Converts detection to Region of Interest for landmark model Dependencies:
	•	calculateAlignmentPointsRects() - Calculates aligned bounding box
	•	transformNormalizedRect() - Applies scaling and rotation
Phase 2: Landmark Extraction Pipeline
2.1 poseLandmarksByRoi() - Core Processing
// Location: detector.ts, line ~347
private async poseLandmarksByRoi(roi: Rect, image?: tf.Tensor3D): Promise<PoseLandmarksByRoiResult>
Purpose: Extracts 39 3D landmarks from ROI Model Outputs:
	•	ld_3d: [1, 195] tensor (39 landmarks × 5 values each)
	•	output_poseflag: [1, 1] confidence score
	•	activation_heatmap: [1, 64, 64, 39] refinement data
	•	world_3d: [1, 117] world coordinates (39 × 3)
	•	activation_segmentation: [256, 256] mask (if enabled)
2.2 tensorsToPoseLandmarksAndSegmentation() - Tensor Processing
// Location: detector.ts, line ~443
private async tensorsToPoseLandmarksAndSegmentation(tensors: tf.Tensor[])
Critical Missing Functions:
	1	tensorsToLandmarks() - Converts raw tensor to normalized landmarks
	2	refineLandmarksFromHeatmap() - Uses heatmap for sub-pixel accuracy
	3	calculateScoreCopy() - Transfers visibility scores
	4	tensorsToSegmentation() - Processes segmentation mask
Phase 3: Coordinate Transformation
3.1 poseLandmarksAndSegmentationInverseProjection()
// Location: detector.ts, line ~401
Missing Functions:
	1	removeLandmarkLetterbox() - Removes padding from landmarks
	2	calculateLandmarkProjection() - Projects to image coordinates
	3	calculateWorldLandmarkProjection() - Projects 3D coordinates
	4	calculateInverseMatrix() - For segmentation transformation
	5	getProjectiveTransformMatrix() - Affine transformation matrix
Phase 4: Post-Processing
4.1 poseLandmarkFiltering() - Temporal Smoothing
// Location: detector.ts, line ~618
Missing Classes:
	1	LowPassVisibilityFilter - Smooths visibility scores
	2	KeypointsSmoothingFilter - Smooths keypoint positions
4.2 Final Output Processing
// Location: detector.ts, line ~223
Missing Function:
	•	normalizedKeypointsToKeypoints() - Denormalizes to pixel coordinates
🚨 Critical Missing Components in Your Implementation
1. Tensor Processing Pipeline
// You need these functions to process model outputs:
- tensorsToLandmarks(tensor, config)
- refineLandmarksFromHeatmap(landmarks, heatmap, config)
- tensorsToDetections(tensors, anchors, config)
- tensorsToSegmentation(tensor, config)
2. Coordinate Transformation Pipeline
// Essential for converting between coordinate systems:
- calculateLandmarkProjection(landmarks, roi)
- calculateWorldLandmarkProjection(worldLandmarks, roi)
- normalizedKeypointsToKeypoints(keypoints, imageSize)
- removeLandmarkLetterbox(landmarks, padding)
3. Image Processing Pipeline
// Required for proper input preprocessing:
- convertImageToTensor(image, config)
- getImageSize(image)
- toImageTensor(image)
4. Geometric Utilities
// Needed for ROI and transformation calculations:
- calculateAlignmentPointsRects(detection, imageSize, config)
- transformNormalizedRect(rect, imageSize, config)
- calculateInverseMatrix(matrix)
- getProjectiveTransformMatrix(inverseMatrix, inputSize, outputSize)
5. Filtering/Smoothing Classes
// For temporal consistency:
- LowPassVisibilityFilter
- KeypointsSmoothingFilter
🔧 Implementation Strategy
Step 1: Import Shared Calculators
// Add to your detector.ts imports:
import {tensorsToLandmarks} from '../shared/calculators/tensors_to_landmarks';
import {refineLandmarksFromHeatmap} from '../shared/calculators/refine_landmarks_from_heatmap';
// ... (all other missing imports)
Step 2: Copy Required Shared Files
You need to copy the entire shared/ directory structure from the pose-detection repository, specifically:
	•	/shared/calculators/ (22 files)
	•	/shared/filters/ (2 files)
	•	/shared/calculators/interfaces/ (all interface files)
Step 3: Configuration Constants
Ensure all constants from constants.ts are properly used:
BLAZEPOSE_TENSORS_TO_LANDMARKS_CONFIG
BLAZEPOSE_REFINE_LANDMARKS_FROM_HEATMAP_CONFIG
BLAZEPOSE_LANDMARKS_SMOOTHING_CONFIG_ACTUAL
// etc.
📊 Data Flow Example
// 1. Raw image input
const image: HTMLVideoElement

// 2. Detection phase
const imageTensor = convertImageToTensor(image, config)
const detections = await detectPose(imageTensor)
const roi = poseDetectionToRoi(detections[0], imageSize)

// 3. Landmark extraction
const roiTensor = convertImageToTensor(image, config, roi)
const modelOutputs = await landmarkModel.predict(roiTensor)

// 4. Tensor processing (YOUR MISSING PIECE)
const landmarks = await tensorsToLandmarks(modelOutputs[0], config)
const refinedLandmarks = await refineLandmarksFromHeatmap(landmarks, modelOutputs[2], config)

// 5. Coordinate transformation
const imageLandmarks = calculateLandmarkProjection(refinedLandmarks, roi)
const worldLandmarks = calculateWorldLandmarkProjection(modelOutputs[3], roi)

// 6. Final output
const keypoints = normalizedKeypointsToKeypoints(imageLandmarks, imageSize)
🎯 Why Your Current Implementation Shows NaN
Your implementation is calling estimatePoses() which returns raw model outputs, but you're not processing them through the required tensor-to-landmark pipeline. The model outputs 5 tensors that need specific transformations to become usable coordinates.
📋 Implementation Checklist
	•	[ ] Copy /shared/calculators/ directory (22 files)
	•	[ ] Copy /shared/filters/ directory (2 files)
	•	[ ] Copy /shared/calculators/interfaces/ directory
	•	[ ] Import all missing functions in detector.ts
	•	[ ] Implement tensor processing in estimatePoses()
	•	[ ] Add coordinate transformation pipeline
	•	[ ] Enable temporal smoothing filters
	•	[ ] Connect keypoint names from constants
