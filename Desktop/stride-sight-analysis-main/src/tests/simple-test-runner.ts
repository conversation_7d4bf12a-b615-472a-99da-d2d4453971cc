/**
 * Simple Test Runner - Direct execution without complex imports
 */

console.log('🧪 BlazePose Simple Test Runner');
console.log('===============================');

// Test 1: Check if basic imports work
console.log('📋 Test 1: Basic Import Test');
try {
  // Try to import TensorFlow
  const tf = require('@tensorflow/tfjs-core');
  console.log('✅ TensorFlow import successful');
  
  // Try to import one of our calculator functions
  const { calculateLandmarkProjection } = require('../shared/calculators/calculate_landmark_projection');
  console.log('✅ Calculator function import successful');
  
} catch (error) {
  console.log('❌ Import test failed:', error.message);
}

// Test 2: Check if TensorFlow can initialize
console.log('');
console.log('📋 Test 2: TensorFlow Initialization Test');
try {
  const tf = require('@tensorflow/tfjs-core');
  
  // Create a simple tensor to test TensorFlow functionality
  const testTensor = tf.tensor2d([[1, 2], [3, 4]]);
  console.log('✅ TensorFlow tensor creation successful');
  
  // Clean up
  testTensor.dispose();
  console.log('✅ TensorFlow tensor disposal successful');
  
} catch (error) {
  console.log('❌ TensorFlow initialization test failed:', error.message);
}

// Test 3: Check if our calculator functions exist and can be called
console.log('');
console.log('📋 Test 3: Calculator Function Existence Test');
try {
  const calculatorFiles = [
    'calculate_landmark_projection',
    'calculate_world_landmark_projection', 
    'normalized_keypoints_to_keypoints',
    'remove_landmark_letterbox',
    'non_max_suppression',
    'remove_detection_letterbox',
    'convert_image_to_tensor'
  ];
  
  let successCount = 0;
  let failCount = 0;
  
  for (const file of calculatorFiles) {
    try {
      const module = require(`../shared/calculators/${file}`);
      console.log(`✅ ${file} module loaded successfully`);
      successCount++;
    } catch (error) {
      console.log(`❌ ${file} module failed to load: ${error.message}`);
      failCount++;
    }
  }
  
  console.log(`📊 Calculator modules: ${successCount} successful, ${failCount} failed`);
  
} catch (error) {
  console.log('❌ Calculator function test failed:', error.message);
}

// Test 4: Check if test classes can be instantiated
console.log('');
console.log('📋 Test 4: Test Class Instantiation Test');
try {
  // Try to load the test classes
  const { BlazePoseUnitTests } = require('./blazepose-unit-tests');
  const unitTests = new BlazePoseUnitTests();
  console.log('✅ BlazePoseUnitTests class instantiated successfully');
  
  const { BlazePoseIntegrationTests } = require('./blazepose-integration-tests');
  const integrationTests = new BlazePoseIntegrationTests();
  console.log('✅ BlazePoseIntegrationTests class instantiated successfully');
  
  const { BlazePosePerformanceTests } = require('./blazepose-performance-tests');
  const performanceTests = new BlazePosePerformanceTests();
  console.log('✅ BlazePosePerformanceTests class instantiated successfully');
  
} catch (error) {
  console.log('❌ Test class instantiation failed:', error.message);
}

console.log('');
console.log('🎯 Simple Test Runner Complete');
console.log('==============================');
console.log('If all tests above passed, the test infrastructure should work.');
console.log('If any failed, those issues need to be resolved before running full tests.');
