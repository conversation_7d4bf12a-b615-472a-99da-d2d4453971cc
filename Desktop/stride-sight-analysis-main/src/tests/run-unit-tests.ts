#!/usr/bin/env node

/**
 * Unit Test Runner for BlazePose Validation
 */

import { blazePoseTestRunner } from './blazepose-test-runner';

async function main() {
  console.log('🧪 BlazePose Unit Tests');
  console.log('======================');
  console.log('');

  try {
    const results = await blazePoseTestRunner.runUnitTestsOnly();
    
    console.log('📊 UNIT TEST RESULTS');
    console.log('====================');
    console.log(`Passed: ${results.passed}`);
    console.log(`Failed: ${results.failed}`);
    console.log(`Success Rate: ${(results.passed / (results.passed + results.failed) * 100).toFixed(1)}%`);
    console.log('');

    console.log('📋 DETAILED RESULTS');
    console.log('===================');
    results.results.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.testName} (${test.duration.toFixed(1)}ms)`);
      if (test.error) {
        console.log(`   Error: ${test.error}`);
      }
      if (test.details) {
        console.log(`   Details: ${JSON.stringify(test.details)}`);
      }
    });

    process.exit(results.failed > 0 ? 1 : 0);

  } catch (error) {
    console.error('❌ Unit test execution failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
