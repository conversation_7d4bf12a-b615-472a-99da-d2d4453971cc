import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';

type AppState = 'upload' | 'processing' | 'results';
type AnalysisMode = '2D' | '3D';
type ActivityType = 'Running' | 'Cycling';

interface VideoFile {
  file: File;
  url: string;
  name: string;
}

const Index = () => {
  const [currentState, setCurrentState] = useState<AppState>('upload');



  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold text-gray-900">
            Stride Sight Analysis - 2D Pipeline Test
          </h1>
          <p className="text-lg text-gray-600">
            Testing basic application functionality
          </p>
        </div>

        {/* Simple Test Card */}
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-2xl font-semibold mb-4">Application Status</h2>
            <p className="text-green-600 font-medium">✅ Application loaded successfully!</p>
            <p className="text-gray-600 mt-2">
              Current state: {currentState}
            </p>
            <button
              onClick={() => setCurrentState(currentState === 'upload' ? 'processing' : 'upload')}
              className="mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Toggle State (Current: {currentState})
            </button>
          </CardContent>
        </Card>

        {/* Test Message */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800">
            🔧 <strong>Debug Mode:</strong> This is a simplified version to test basic functionality.
            Once this loads properly, we can gradually add back the pose detection components.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
